"use client";

import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import Plans from "./plans-subscription";

export default function PlanDialog() {
	const { planBoxOpen, setPlanBoxOpen } = usePlanBoxOpenStore();

	return (
		<Dialog open={planBoxOpen} onOpenChange={setPlanBoxOpen}>
			<DialogContent className="gap-0 rounded-lg p-0 sm:max-w-4xl">
				<DialogTitle className="" />
				<ScrollArea className="max-h-[90vh] overflow-y-auto">
					<div className="flex flex-col items-center gap-8 p-8">
						<p className="text-2xl font-semibold">Upgrade to unlock high-quality Lip Sync</p>
						<Plans hasFree={false} />
					</div>
				</ScrollArea>
			</DialogContent>
		</Dialog>
	);
}
