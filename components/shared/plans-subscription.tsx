"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2, Check, X, Info, RocketIcon } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { pricingPlans, PricingPlan } from "@/config/pricing";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Subscription } from "@/@types/subscription";
import { AuthError, handleError } from "@/@types/error";
import { useRouter } from "nextjs-toploader/app";
import { Al<PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ofetch } from "ofetch";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { canChangePlan } from "@/lib/utils-membership";
import { EVENT_CHECKOUT } from "@/lib/track-events";
import { sendGTMEvent } from "@next/third-parties/google";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Hint } from "../ui/custom/hint";

export default function Plans({ hasFree = true, userSubscription }: { hasFree?: boolean; userSubscription?: Subscription | null }) {
	const router = useRouter();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();

	const [currentTab, setCurrentTab] = useState("yearly");

	// const [isCancelling, setIsCancelling] = useState(false);
	const [isPurchasing, setIsPurchasing] = useState(false);
	const [purchaseProductId, setPurchaseProductId] = useState<string | null>(null);
	const [subscriptionChanged, setSubscriptionChanged] = useState(false);

	const purchase = async (productId: string, planName?: string) => {
		if (userSubscription) return;
		console.log("New subscription checkout");
		sendGTMEvent({ event: EVENT_CHECKOUT, checkout_plan: `${planName}(${currentTab === "monthly" ? "Monthly" : "Yearly"})` });

		try {
			setIsPurchasing(true);
			setPurchaseProductId(productId);

			const { status, message, url } = await ofetch("/api/payment/checkout", {
				method: "POST",
				body: { productId, type: "subscription" },
			});
			if (status === 1001) {
				toast.error("You are already subscribed.");
				setIsPurchasing(false);
				return;
			}
			handleError(status, message);
			if (url) {
				router.push(url);
				// window.open(url, "_self");
			}
		} catch (error) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
			} else {
				toast.error("Error creating a checkout.");
			}
			setIsPurchasing(false);
			setPurchaseProductId(null);
		}
	};
	const changePlan = async (productId: string) => {
		if (!userSubscription) return;
		if (userSubscription.productId === productId) return;
		if (isPurchasing) return;
		console.log("Change subscription");

		try {
			setPurchaseProductId(productId);
			setIsPurchasing(true);
			const { status, message } = await ofetch("/api/payment/subscription", {
				method: "POST",
				body: {
					type: "change",
					subscriptionId: userSubscription.subscriptionId,
					productId,
				},
			});
			handleError(status, message);
			setSubscriptionChanged(true);
			toast.success("Your subscription change has been initiated.");
		} catch (error) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error("Subscription change failed.");
		} finally {
			setIsPurchasing(false);
			setPurchaseProductId(null);
		}
	};
	// const cancelSubscription = async () => {
	// 	if (!subscription) return;
	// 	if (isCancelling) return;

	// 	try {
	// 		setIsCancelling(true);

	// 		const { status, message } = await ofetch("/api/v1/user/subscription/cancel", {
	// 			method: "POST",
	// 			body: { subscriptionId: subscription.subscriptionId },
	// 		});
	// 		handleError(status, message);
	// 		// const { status, message } = await cancelSubAction(subscription.subscriptionId);
	// 		// handleActionError(status, message);
	// 		router.refresh();
	// 		toast.success("Subscription cancelled.");
	// 		setUserPlanBoxOpen(false);
	// 		setUserSubscription(null);
	// 	} catch (error) {
	// 		console.error(error);
	// 		if (error instanceof AuthError) {
	// 			setSignInBoxOpen(true);
	// 			return;
	// 		}
	// 		toast.error("Error cancelling subscription.");
	// 	} finally {
	// 		setIsCancelling(false);
	// 	}
	// };

	return (
		<div className="relative flex w-full flex-col gap-12">
			<div className="flex items-center justify-center">
				<Tabs value={currentTab} onValueChange={setCurrentTab} className="">
					<TabsList className="h-[46px] p-1">
						<TabsTrigger
							value="monthly"
							className="px-4 font-normal dark:data-[state=active]:border-none dark:data-[state=active]:bg-zinc-200 dark:data-[state=active]:text-zinc-900"
						>
							Monthly
						</TabsTrigger>
						<TabsTrigger
							value="yearly"
							className="px-4 font-normal dark:data-[state=active]:border-none dark:data-[state=active]:bg-zinc-200 dark:data-[state=active]:text-zinc-900"
						>
							Yearly <span className="rounded-sm bg-yellow-300 px-1.5 py-0.5 font-mono text-xs text-zinc-900">-30%</span>
						</TabsTrigger>
					</TabsList>
				</Tabs>
			</div>

			{subscriptionChanged && (
				<div className="absolute z-20 w-full">
					<Alert className="mx-auto max-w-xl items-center shadow-xl">
						<RocketIcon className="h-4 w-4 text-indigo-500" />
						<AlertTitle className="text-zinc-100">Your subscription change has been initiated.</AlertTitle>
						<AlertDescription className="text-muted-foreground inline text-sm">
							Your order is being processed. It should be completed in 20 seconds to 2 minutes. You can{" "}
							<span className="inline text-indigo-400 italic">refresh the page to check the status</span>.
						</AlertDescription>
					</Alert>
				</div>
			)}

			<div
				className={cn(
					"mx-auto inline-grid w-full grid-cols-1 justify-center gap-4 text-start sm:grid-cols-2 md:gap-6",
					hasFree ? "lg:grid-cols-4" : "lg:grid-cols-3",
				)}
			>
				{pricingPlans.map((pricing: PricingPlan, index: number) => {
					if (!hasFree && pricing.free) return null;
					return (
						<Card
							key={index}
							className={cn(
								"relative mx-auto w-full max-w-full gap-1 rounded-xl border-none bg-zinc-900 shadow-none",
								// pricing.badge && "border-brand-success border hover:bg-zinc-900/80",
							)}
						>
							<CardHeader className="h-20 gap-0">
								<CardTitle className="flex flex-col gap-1">
									<div className="flex flex-row items-center justify-between gap-1 text-xl font-medium">
										<p className={pricing.titleClassName}>{pricing.title}</p>
										{pricing.badge && (
											<Badge variant="secondary" className="bg-brand-success/60 hover:bg-brand-success/60 bg-linear-to-r font-normal">
												{pricing.badge}
											</Badge>
										)}
									</div>
									{pricing.description && <p className="text-muted-foreground text-sm font-normal">{pricing.description}</p>}
								</CardTitle>
								<CardDescription className="h-0"></CardDescription>
							</CardHeader>

							<CardContent className="flex flex-col gap-0">
								<div className="mt-4 mb-10 flex flex-wrap items-end">
									{!pricing.free && currentTab === "yearly" && (
										<span className="font-heading mr-2 text-xl font-medium text-gray-500 line-through">
											{pricing.currency.symbol}
											{pricing.price.monthly}
										</span>
									)}
									<span className="font-heading text-4xl font-medium">
										{pricing.currency.symbol}
										{pricing.free || currentTab === "monthly" ? pricing.price.monthly : pricing.price.monthForYearly}
									</span>
									<div className="mb-1 ml-1 leading-none">
										<p className="text-muted-foreground font- text-sm">{!pricing.free && pricing.duration}</p>
									</div>
								</div>

								<div className="h-20">
									{pricing.free ? (
										<Button variant="secondary" disabled className="font-heading w-full max-w-xs rounded-full">
											Get started
										</Button>
									) : !userSubscription ? (
										<Button
											{...{
												// variant: pricing.badge ? "secondary" : "default",
												variant: "secondary",
												disabled:
													isPurchasing &&
													purchaseProductId === (currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly),
											}}
											className={cn("font-heading w-full rounded-full", "bg-brand-success hover:bg-brand-success/80")}
											onClick={() => {
												purchase(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly, pricing.title);
											}}
										>
											<p className="flex flex-row items-center space-x-1">
												<span>Subscribe now</span>
												{isPurchasing &&
													purchaseProductId ===
														(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly) && (
														<Loader2 className="h-4 w-4 animate-spin" />
													)}
											</p>
										</Button>
									) : userSubscription.productId === (currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly) ? (
										<Button
											{...{
												// variant: pricing.badge ? "secondary" : "default",
												variant: "secondary",
											}}
											disabled
											className={cn("font-heading w-full rounded-full", "bg-brand-success hover:bg-brand-success/80")}
										>
											<p className="flex flex-row items-center space-x-1">Current plan</p>
										</Button>
									) : (
										<AlertDialog>
											<AlertDialogTrigger asChild>
												<Button
													{...{
														// variant: pricing.badge ? "secondary" : "default",
														variant: "secondary",
														disabled:
															(isPurchasing &&
																purchaseProductId ===
																	(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly)) ||
															!canChangePlan(
																userSubscription.productId,
																currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly,
															),
													}}
													className={cn("font-heading w-full rounded-full", "bg-brand-success hover:bg-brand-success/80")}
												>
													<p className="flex flex-row items-center space-x-1">
														<span>Change</span>
														{isPurchasing &&
															purchaseProductId ===
																(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly) && (
																<Loader2 className="h-4 w-4 animate-spin" />
															)}
													</p>
												</Button>
											</AlertDialogTrigger>
											<AlertDialogContent>
												<AlertDialogHeader>
													<AlertDialogTitle>Are you sure you want to change your subscription plan?</AlertDialogTitle>
													<AlertDialogDescription>
														Your plan will change right away. We’ll adjust your bill to match your new plan.
													</AlertDialogDescription>
												</AlertDialogHeader>
												<AlertDialogFooter>
													<AlertDialogCancel>Cancel</AlertDialogCancel>
													<AlertDialogAction
														onClick={() => {
															changePlan(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly);
														}}
													>
														Change
													</AlertDialogAction>
												</AlertDialogFooter>
											</AlertDialogContent>
										</AlertDialog>
									)}
								</div>

								<div className="flex flex-col gap-4">
									<div className="flex flex-col gap-3 text-xs text-zinc-300">
										{pricing.features?.map((feature: any, index: any) => (
											<p key={index} className="flex items-start gap-2">
												<Check className="h-4 w-4 shrink-0 text-zinc-100" />
												<span className="inline-flex items-center gap-1 font-[380]">
													{feature.description}
													{feature.tips && (
														<Hint label={feature.tips}>
															<Info className="text-muted-foreground inline size-3.5 cursor-pointer" />
														</Hint>
													)}
												</span>
											</p>
										))}

										{pricing.unFeatures?.map((feature: any, index: any) => (
											<p key={index} className="flex items-start gap-2">
												<X className="h-4 w-4 shrink-0 text-red-700" />
												<span className="">{feature}</span>
											</p>
										))}
									</div>
								</div>
							</CardContent>
						</Card>
					);
				})}
			</div>
		</div>
	);
}
