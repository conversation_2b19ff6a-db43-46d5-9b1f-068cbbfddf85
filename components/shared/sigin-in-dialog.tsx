"use client";

import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import SignIn from "./sigin-in";
import { DialogDescription } from "@radix-ui/react-dialog";

export default function SignInDialog() {
	const { signInBoxOpen, setSignInBoxOpen } = useSignInBoxOpenStore();

	return (
		<Dialog open={signInBoxOpen} onOpenChange={setSignInBoxOpen}>
			<DialogContent className="max-w-sm gap-0 rounded-lg bg-zinc-900 py-8">
				<DialogTitle></DialogTitle>
				<DialogDescription className="h-0"></DialogDescription>
				<SignIn />
			</DialogContent>
		</Dialog>
	);
}
