"use client";

import React, { useState } from "react";
import { ChevronDown, CreditCard, MenuIcon, Power, History, CoinsIcon, MailIcon } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Logo } from "@/components/logo";
import { useSession } from "@/lib/auth-client";
import { EMAIL_CONTACT, WEBNAME } from "@/lib/constants";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import {
	NavigationMenu,
	NavigationMenuContent,
	NavigationMenuItem,
	NavigationMenuLink,
	NavigationMenuList,
	NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useUserStore } from "@/store/useUserStore";
import { MembershipID } from "@/@types/membership-type";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { useSignOut } from "@/hooks/use-signout";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";
import { useRouter } from "nextjs-toploader/app";
import { Badge } from "../ui/badge";
import { EVENT_OPEN_PLAN_ID } from "@/lib/track-events";

interface MenuItem {
	name: string;
	href?: string;
	icon?: React.ReactNode;
	target?: string;
	hidden?: boolean;
	items?: {
		name: string;
		href: string;
		description?: string;
		new?: boolean;
	}[];
}

const menuItems: MenuItem[] = [
	{ name: "Al Lip Sync", href: "/" },
	{ name: "FAQs", href: "/#faqs" },
	// { name: "Blog", href: "/blog" },
	{ name: "Pricing", href: "/pricing" },
];

export const Header = () => {
	const router = useRouter();
	const { data: session } = useSession();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();
	const { handleSignOut } = useSignOut();
	const { user, creditsAll: userCreditsAll, hasPaid: userHasPaid } = useUserStore();

	const [showMobileMenu, setShowMobileMenu] = useState<boolean>(false);

	return (
		<header className="bg-background sticky top-0 z-20 w-full transition-colors duration-300">
			{/* <BannerTop /> */}
			<div className="container flex h-14 flex-wrap items-center justify-between px-4 md:px-6">
				<div className="flex flex-row items-center gap-8">
					<NoPrefetchLink href="/" className="flex items-center gap-2 rtl:space-x-reverse">
						<Logo className="rounded" />
						<span className="font-heading hidden text-xl font-medium md:block">{WEBNAME}</span>
					</NoPrefetchLink>
				</div>

				<div className="hidden rounded-lg font-normal md:flex-row lg:flex">
					<NavigationMenu viewport={false}>
						<NavigationMenuList className="space-x-0">
							{menuItems.map((route, index) => (
								<React.Fragment key={index}>
									{route.items ? (
										<NavigationMenuItem>
											<NavigationMenuTrigger className="bg-transparent px-3 font-[350] text-zinc-300 hover:bg-transparent focus:bg-transparent data-active:bg-transparent data-[state=open]:bg-transparent">
												{route.name}
											</NavigationMenuTrigger>
											<NavigationMenuContent>
												<div className="space-y-2">
													{/* <p className="text-sm font-medium">{route.name}</p> */}
													<div className="flex w-[240px] flex-col gap-0.5">
														{route.items.map((feature, index) => (
															<NavigationMenuLink key={index} asChild>
																<NoPrefetchLink href={feature.href}>
																	<div className="flex flex-row items-center">
																		{feature.name}
																		{feature.new && (
																			<Badge
																				variant="secondary"
																				className="ml-1 rounded-full bg-green-500 px-1.5 py-0.5 text-[10px]"
																			>
																				New
																			</Badge>
																		)}
																	</div>
																	{feature.description && <div className="text-muted-foreground">{feature.description}</div>}
																</NoPrefetchLink>
															</NavigationMenuLink>
														))}
													</div>
												</div>
											</NavigationMenuContent>
										</NavigationMenuItem>
									) : (
										<NavigationMenuItem>
											<NavigationMenuLink className={cn("hover:text-accent-foreground px-3 py-2 hover:bg-transparent")} asChild>
												<NoPrefetchLink
													href={route.href!}
													className="flex flex-row items-center font-[350] text-zinc-300"
													target={route.target}
												>
													{route.name}
													{route.icon && <>{route.icon}</>}
												</NoPrefetchLink>
											</NavigationMenuLink>
										</NavigationMenuItem>
									)}
								</React.Fragment>
							))}
							{session && (
								<NavigationMenuItem>
									<NavigationMenuLink className={cn("hover:text-accent-foreground px-3 py-2 hover:bg-transparent")} asChild>
										<NoPrefetchLink href="/my-creations" className="flex flex-row items-center font-[350] text-zinc-300">
											My Creations
										</NoPrefetchLink>
									</NavigationMenuLink>
								</NavigationMenuItem>
							)}
						</NavigationMenuList>
					</NavigationMenu>
				</div>

				<div className="flex flex-row items-center gap-3">
					{session ? (
						<>
							{/* {!userHasPaid && (
								<Button
									id={EVENT_OPEN_PLAN_ID}
									size="sm"
									variant="secondary"
									className="bg-brand-success hover:bg-background/80 cursor-pointer rounded-sm text-[13px] font-normal"
									onClick={() => setPlanBoxOpen(true)}
								>
									Upgrade
								</Button>
							)} */}
							<div className="flex flex-row items-center gap-1 rounded-full bg-amber-500/10 px-3 py-1.5 text-sm font-medium text-amber-600 dark:text-amber-400">
								<CoinsIcon className="size-4" /> <span>{userCreditsAll}</span>
							</div>
							<DropdownMenu modal={false}>
								<DropdownMenuTrigger asChild className="cursor-pointer">
									<div className="flex shrink-0 flex-row items-center gap-2">
										<Avatar className="h-7 w-7">
											<AvatarImage src={session?.user.image!} alt="User Avatar" />
											<AvatarFallback>{session?.user.name}</AvatarFallback>
										</Avatar>
										<ChevronDown className="size-3.5 shrink-0 text-neutral-600 lg:block" />
									</div>
								</DropdownMenuTrigger>
								<DropdownMenuContent className="w-[280px] p-0" align="end" forceMount>
									<div className="flex gap-5 p-5">
										<Avatar className="flex size-9 shrink-0 items-center gap-2 text-neutral-800">
											<AvatarImage src={session?.user.image!} alt="User Avatar" />
											<AvatarFallback>{session?.user.name}</AvatarFallback>
										</Avatar>
										<div className="flex min-w-0 flex-1 flex-col items-start">
											<p className="truncate text-sm font-semibold">{session?.user.name ?? WEBNAME}</p>
											<p className="text-muted-foreground mt-1 truncate text-xs">{session?.user.email ?? "--"}</p>
										</div>
									</div>
									{user?.membershipId === MembershipID.Free && (
										<div className="px-[24px] pb-5">
											<Button
												id={EVENT_OPEN_PLAN_ID}
												size="sm"
												variant="secondary"
												className="bg-brand-success hover:bg-brand-success/80 w-full"
												onClick={() => setPlanBoxOpen(true)}
											>
												Get a plan
											</Button>
										</div>
									)}
									{/* <div className="flex flex-row items-center justify-between px-[24px] pb-4">
										<div className="flex flex-row items-center gap-1 text-sm font-[350]">
											<Icon iconNode={coinsStack} className="size-4 text-green-500" />
											{userCreditsAll} credits
										</div>
										<Button
											id={EVENT_OPEN_PLAN_ID}
											size="sm"
											variant="secondary"
											className="cursor-pointer bg-indigo-500 hover:bg-indigo-600"
											onClick={() => setPlanBoxOpen(true)}
										>
											Get more
										</Button>
									</div> */}
									<Separator />

									<NoPrefetchLink
										href="/my-creations"
										className="hover:bg-muted flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100"
									>
										<div className="flex flex-row items-center">
											<History className="mr-5 h-4 w-4 shrink-0" />
											My Creations
										</div>
									</NoPrefetchLink>
									<NoPrefetchLink
										href="/user/my-billing"
										className="hover:bg-muted flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100"
									>
										<div className="flex flex-row items-center">
											<CreditCard className="mr-5 h-4 w-4 shrink-0" />
											My Billing
										</div>
										<p className="flex items-center gap-1 rounded bg-zinc-700 px-2 py-1 text-xs font-medium text-zinc-100">
											{user?.membershipFormatted}
										</p>
									</NoPrefetchLink>
									<a
										href={`mailto:${EMAIL_CONTACT}`}
										target="_blank"
										className="flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100 hover:bg-zinc-700 hover:text-zinc-200"
									>
										<div className="flex flex-row items-center">
											<MailIcon className="mr-5 h-4 w-4 shrink-0" />
											Contact Us
										</div>
									</a>
									{/* <NoPrefetchLink
										href={FEEDBACK_URL}
										target="_blank"
										className="flex h-11 w-full cursor-pointer items-center px-[24px] text-xs transition-all duration-100 hover:bg-muted"
									>
										<MessageCircle className="mr-5 h-4 w-4 shrink-0" />
										Got Feedback
									</NoPrefetchLink> */}

									<Separator />

									<button
										className="hover:bg-muted flex h-11 w-full cursor-pointer items-center px-[24px] text-xs transition-all duration-100"
										onClick={handleSignOut}
									>
										<Power className="mr-5 h-4 w-4 shrink-0" />
										Sign out
									</button>
								</DropdownMenuContent>
							</DropdownMenu>
						</>
					) : (
						<Button size="sm" variant="secondary" onClick={() => setSignInBoxOpen(true)} className="bg-brand-success hover:bg-brand-success/80">
							Sign In
						</Button>
					)}
					<Button
						size="icon"
						variant="outline"
						className="dark:border-muted dark:bg-background size-8 lg:hidden"
						onClick={() => setShowMobileMenu(true)}
					>
						<MenuIcon />
					</Button>
				</div>
			</div>

			<Dialog open={showMobileMenu} onOpenChange={setShowMobileMenu}>
				<DialogContent className="h-[97vh] w-[95vw] max-w-full rounded bg-zinc-900">
					<DialogHeader>
						<DialogTitle className="text-start">
							<NoPrefetchLink href="/" className="flex items-center space-x-2">
								<Logo />
								<span className="font-heading text-lg font-semibold">{WEBNAME}</span>
							</NoPrefetchLink>
						</DialogTitle>
						<div className="h-full pt-3 text-start">
							{menuItems.map((route, index) => (
								<React.Fragment key={index}>
									{route.items ? (
										<div className="space-y-2">
											<Accordion type="single" collapsible>
												<AccordionItem value="item-1">
													<AccordionTrigger className="py-3 text-base font-normal hover:no-underline">{route.name}</AccordionTrigger>
													<AccordionContent className="space-y-2">
														{route.items.map((route, index) => (
															<div key={index} className="text-zinc-200">
																<NoPrefetchLink href={route.href} className="" onClick={() => setShowMobileMenu(false)}>
																	<p className="items-center">{route.name}</p>
																</NoPrefetchLink>
															</div>
														))}
													</AccordionContent>
												</AccordionItem>
											</Accordion>
										</div>
									) : (
										<div className="py-3">
											<NoPrefetchLink
												href={route.href!}
												className="font-normal"
												target={route.target}
												onClick={() => setShowMobileMenu(false)}
											>
												<p className="items-center">
													{route.name}
													{route.icon && <>{route.icon}</>}
												</p>
											</NoPrefetchLink>
										</div>
									)}
									<Separator className="" />
								</React.Fragment>
							))}

							{session && (
								<div className="py-3">
									<NoPrefetchLink href="/my-creations" className="font-normal" onClick={() => setShowMobileMenu(false)}>
										<p className="items-center">My Creations</p>
									</NoPrefetchLink>
								</div>
							)}
						</div>
						{/* <DialogFooter>
							{session ? (
								<NoPrefetchLink href={ROUTE_PATH_SIGN_IN} className={cn(buttonVariants(), "mx-auto")}>
									Dashboard
								</NoPrefetchLink>
							) : (
								<NoPrefetchLink href={ROUTE_PATH_SIGN_IN} className={cn(buttonVariants(), "mx-auto")}>
									Get Started
								</NoPrefetchLink>
							)}
						</DialogFooter> */}
					</DialogHeader>
				</DialogContent>
			</Dialog>
		</header>
	);
};
