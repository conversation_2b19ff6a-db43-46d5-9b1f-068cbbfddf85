"use client";

import { useCallback, useRef, useState } from "react";
import { toast } from "sonner";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ofetch } from "ofetch";
import { AuthError, IgnoreError, Credits402Error, handleError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { CoinsIcon, CloudUpload, LoaderCircleIcon, Trash2Icon, PlayIcon, PauseIcon, WandSparklesIcon, LayersIcon, CrownIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { useSubmitTimer } from "@/hooks/use-submit-timer";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { uploadFile } from "@/lib/file/upload-file";
import { sendGTMEvent } from "@next/third-parties/google";
import { MEDIA_FILE_DURATION_LIMIT, MEDIA_FILE_DURATION_LIMIT_FREE, MEDIA_FILE_DURATION_LIMIT_STARTER } from "@/lib/constants";
import { useDropzone } from "react-dropzone";
import { Button } from "../ui/button";
import { useWavesurfer } from "@wavesurfer/react";
import { formatMediaTime } from "@/lib/utils-date";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";
import { MembershipID } from "@/@types/membership-type";
import { MediaResultStatus } from "@/@types/media/media-type";
import { EVENT_GEN_LIPSYNC, EVENT_OPEN_PLAN_ID } from "@/lib/track-events";
import { useRouter } from "nextjs-toploader/app";

export default function LipSyncClient() {
	const router = useRouter();
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const [fileVideo, setFileVideo] = useState<{
		url: string;
		localPath: string;
	} | null>(null);
	const [uploadingVideo, setUploadingVideo] = useState<boolean>(false);
	const {
		getRootProps: getRootPropsVideo,
		getInputProps: getInputPropsVideo,
		open: openDropzoneVideo,
	} = useDropzone({
		noClick: true,
		multiple: false,
		accept: {
			"video/mp4": [],
			"video/quicktime": [],
			"video/webm": [],
		},
		maxFiles: 1,
		onDragEnter: () => {
			if (!session) {
				setSignInBoxOpen(true);
				return;
			}
		},
		onError: console.error,
		onDrop: async (acceptedFiles, fileRejections, event) => {
			if (fileRejections.length > 0) {
				const message = fileRejections.at(0)?.errors.at(0)?.message;
				console.error?.(new Error(message));
				return;
			}

			if (!session) {
				setSignInBoxOpen(true);
				return;
			}
			if (!acceptedFiles || acceptedFiles.length === 0) return;
			let file = acceptedFiles[0];

			try {
				const localPath = URL.createObjectURL(file);
				const duration = await new Promise<number>((resolve, reject) => {
					const audio = new Audio();
					audio.src = localPath;
					audio.onloadedmetadata = () => {
						const duration = Number(audio.duration.toFixed(2));
						resolve(duration);
					};
					audio.onerror = (error) => {
						reject(new Error("Error loading audio file"));
					};
				});

				if (!userHasPaid && duration > MEDIA_FILE_DURATION_LIMIT_FREE) {
					toast.error(`Free plan only supports video up to ${MEDIA_FILE_DURATION_LIMIT_FREE} seconds.`, {
						action: (
							<Button
								size="sm"
								variant="secondary"
								onClick={() => setPlanBoxOpen(true)}
								className="bg-brand-success hover:bg-brand-success/80 h-7 px-2 text-xs font-normal"
							>
								Upgrade
							</Button>
						),
					});
					return;
				}
				if (user?.membershipId === MembershipID.Starter && duration > MEDIA_FILE_DURATION_LIMIT_STARTER) {
					toast.error(`Starter plan only supports video up to ${MEDIA_FILE_DURATION_LIMIT_STARTER} seconds.`);
					return;
				}
				if (duration > MEDIA_FILE_DURATION_LIMIT) {
					toast.error(`Video is too long. Please upload a video up to 5 minutes.`);
					return;
				}

				setUploadingVideo(true);

				const { file_url } = await uploadFile(file);
				// const file_url = localPath;
				setFileVideo({
					url: file_url,
					localPath: localPath,
				});
			} catch (error: any) {
				console.error("Failed to upload video:", error.message);
				if (error instanceof AuthError) {
					setSignInBoxOpen(true);
					return;
				}
				toast.error(`Upload video failed: ${error.message}`);
			} finally {
				setUploadingVideo(false);
			}
		},
	});

	const [fileAudio, setFileAudio] = useState<{
		url: string;
		localPath: string;
		fileName: string;
		fileDuration: number;
	} | null>(null);
	const [uploadingAudio, setUploadingAudio] = useState<boolean>(false);
	const {
		getRootProps: getRootPropsAudio,
		getInputProps: getInputPropsAudio,
		open: openDropzoneAudio,
	} = useDropzone({
		noClick: true,
		multiple: false,
		accept: {
			"audio/wav": [],
			"audio/mpeg": [],
			"audio/x-m4a": [],
		},
		maxFiles: 1,
		onError: console.error,
		onDrop: async (acceptedFiles, fileRejections, event) => {
			if (fileRejections.length > 0) {
				const message = fileRejections.at(0)?.errors.at(0)?.message;
				console.error?.(new Error(message));
				return;
			}

			if (!session) {
				setSignInBoxOpen(true);
				return;
			}
			if (!acceptedFiles || acceptedFiles.length === 0) return;
			const file: File = acceptedFiles[0];

			try {
				const localPath = URL.createObjectURL(file);
				const duration = await new Promise<number>((resolve, reject) => {
					const audio = new Audio();
					audio.src = localPath;
					audio.onloadedmetadata = () => {
						const duration = Number(audio.duration.toFixed(2));
						resolve(duration);
					};
					audio.onerror = (error) => {
						reject(new Error("Error loading audio file"));
					};
				});

				if (!userHasPaid && duration > MEDIA_FILE_DURATION_LIMIT_FREE) {
					toast.error(`Free plan only supports audio up to ${MEDIA_FILE_DURATION_LIMIT_FREE} seconds.`, {
						action: (
							<Button
								size="sm"
								variant="secondary"
								onClick={() => setPlanBoxOpen(true)}
								className="bg-brand-success hover:bg-brand-success/80 h-7 px-2 text-xs font-normal"
							>
								Upgrade
							</Button>
						),
					});
					return;
				}
				if (user?.membershipId === MembershipID.Starter && duration > MEDIA_FILE_DURATION_LIMIT_STARTER) {
					toast.error(`Starter plan only supports audio up to ${MEDIA_FILE_DURATION_LIMIT_STARTER} seconds.`);
					return;
				}
				if (duration > MEDIA_FILE_DURATION_LIMIT) {
					toast.error(`Video is too long. Please upload a video up to 5 minutes.`);
					return;
				}

				setUploadingAudio(true);
				const { file_url } = await uploadFile(file);
				// const file_url = localPath;
				setFileAudio({
					url: file_url,
					localPath: localPath,
					fileName: file.name,
					fileDuration: duration,
				});
			} catch (error: any) {
				console.error("Failed to upload audio:", error.message);
				if (error instanceof AuthError) {
					setSignInBoxOpen(true);
					return;
				}
				toast.error(`Upload audio failed: ${error.message}`);
			} finally {
				setUploadingAudio(false);
			}
		},
	});
	const waveformRef = useRef<HTMLDivElement>(null);
	const { wavesurfer, isPlaying, currentTime } = useWavesurfer({
		container: waveformRef,
		height: 88,
		waveColor: "#d4d4d8",
		progressColor: "#71717a",
		cursorColor: "#10b981",
		barWidth: 2,
		barGap: 1,
		barRadius: 2,
		url: fileAudio?.localPath,
	});

	const onPlayPause = useCallback(() => {
		wavesurfer && wavesurfer.playPause();
	}, [wavesurfer]);

	const [resultVideo, setResultVideo] = useState<string | null>(null);
	// const [resultVideo, setResultVideo] = useState<string | null>("https://store.lipsync.studio/latent_lipsync_example3.mp4");
	const [submitting, setSubmitting] = useState(false);
	const { seconds } = useSubmitTimer(submitting);
	const handleGenerate = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		if (!fileVideo?.url || !fileAudio?.url) {
			toast.warning("Please upload video and audio first.");
			return;
		}

		sendGTMEvent({
			event: EVENT_GEN_LIPSYNC,
			membership_level: user?.membershipLevel,
		});

		try {
			setResultVideo(null);
			setSubmitting(true);
			const { status, message, task_status, request_id } = await ofetch("/api/v1/lipsync", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: {
					video: fileVideo?.url,
					audio: fileAudio?.url,
				},
			});
			if (status === 1001) {
				toast.warning(message);
				return;
			}
			handleError(status, message);
			refreshUser();

			let taskStatus = MediaResultStatus.InProgress;
			let taskError = null;
			await new Promise((resolve) => setTimeout(resolve, 60 * 2 * 1000)); // wait for 2 minutes

			// get task status
			while (taskStatus !== MediaResultStatus.Completed && taskStatus !== MediaResultStatus.Failed) {
				await new Promise((resolve) => setTimeout(resolve, 20000)); // wait for 20 seconds
				let {
					status: request_status,
					message: request_message,
					taskStatus: reuqest_taskStatus,
					taskError: request_taskError,
					resultUrl,
				} = await ofetch("/api/v1/lipsync/status", {
					method: "POST",
					body: { id: request_id },
				});
				handleError(request_status, request_message);
				taskStatus = reuqest_taskStatus;
				taskError = request_taskError;
				if (resultUrl) {
					setResultVideo(resultUrl);
				}
			}
			if (taskStatus === MediaResultStatus.Failed) {
				if (taskError) {
					throw new Error(taskError);
				}
				throw new Error("Generate video failed. Please try again or contact support.");
			}

			toast.success("Lip sync success.");
		} catch (error: any) {
			console.error("Failure to generate lip sync.", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.error("You do not have enough credits.", {
					action: (
						<Button
							size="sm"
							variant="secondary"
							onClick={() => {
								if (userHasPaid) {
									router.push("/my-billing");
								} else {
									setPlanBoxOpen(true);
								}
							}}
							className="bg-brand-success hover:bg-brand-success/80 h-7 px-2 text-xs font-normal"
						>
							{userHasPaid ? "My billing" : "Get more"}
						</Button>
					),
				});
				return;
			}
			toast.error(error.message || "Lip sync failed. Please try again later.");
		} finally {
			setSubmitting(false);
		}
	};

	return (
		<>
			<div className="flex w-full flex-col gap-6 rounded-xl bg-zinc-800/50 p-4 md:gap-8 md:p-8">
				<div className="grid w-full gap-4 md:grid-cols-2">
					<div className={cn("space-y-4")}>
						<div className="space-y-[8px]">
							<p className="text-sm text-zinc-300">1. Upload talking video</p>

							{fileVideo ? (
								<div className="group relative h-[180px] w-full rounded-lg border-zinc-700 md:h-[200px] dark:bg-zinc-900/30">
									<video
										src={fileVideo.localPath}
										muted
										controls
										controlsList="nodownload noplaybackrate noplaybackrate nofullscreen"
										disablePictureInPicture
										disableRemotePlayback
										className="h-full w-full rounded-lg object-contain"
									/>
									<Button
										size="icon"
										variant="ghost"
										className="absolute top-2 right-2 rounded-full text-zinc-300"
										onClick={() => setFileVideo(null)}
									>
										<Trash2Icon className="size-4" />
									</Button>
								</div>
							) : (
								<div onClick={() => {}}>
									<div
										className="hover:border-brand-success/50 h-[180px] w-full cursor-pointer flex-col justify-center rounded-lg border-2 border-dashed border-zinc-700 md:h-[200px] dark:bg-zinc-900/30"
										{...getRootPropsVideo()}
										onClick={() => {
											if (!session) {
												setSignInBoxOpen(true);
												return;
											}
											openDropzoneVideo();
										}}
									>
										{uploadingVideo ? (
											<div className="text-muted-foreground flex h-full flex-col items-center justify-center gap-1 text-sm font-normal">
												<LoaderCircleIcon className="text-muted-foreground animate-spin" />
												<p>Uploading...</p>
											</div>
										) : (
											<div className="text-muted-foreground flex h-full flex-col items-center justify-center gap-1 text-sm font-normal">
												<CloudUpload />
												<p>Click or drag video here</p>
											</div>
										)}
									</div>
									<input {...getInputPropsVideo()} />
								</div>
							)}
						</div>
					</div>
					<div className={cn("space-y-4")}>
						<div className="space-y-[8px]">
							<p className="text-sm text-zinc-300">2. Upload audio</p>

							{fileAudio ? (
								<div className="group relative flex h-[180px] w-full flex-col justify-between rounded-lg border-zinc-700 px-3 py-1.5 md:h-[200px] dark:bg-zinc-900/30">
									<div className="flex flex-row items-center justify-between">
										<p className="line-clamp-1 text-sm">{fileAudio.fileName}</p>
										<Button size="icon" variant="ghost" className="-mr-1.5 rounded-full text-zinc-300" onClick={() => setFileAudio(null)}>
											<Trash2Icon className="size-4" />
										</Button>
									</div>
									<div ref={waveformRef} className="h-auto" />
									<div className="flex flex-row items-center justify-between">
										<Button
											size="icon"
											variant="secondary"
											onClick={onPlayPause}
											className="rounded-full bg-zinc-700/70 text-zinc-200 hover:bg-zinc-700/90"
										>
											{isPlaying ? <PauseIcon className="fill-current" /> : <PlayIcon className="fill-current" />}
										</Button>
										<p className="line-clamp-1 text-sm">
											{formatMediaTime(currentTime)}/{formatMediaTime(fileAudio.fileDuration)}
										</p>
									</div>
								</div>
							) : (
								<div onClick={() => {}}>
									<div
										className="hover:border-brand-success/50 h-[180px] w-full cursor-pointer flex-col justify-center rounded-lg border-2 border-dashed border-zinc-700 md:h-[200px] dark:bg-zinc-900/30"
										{...getRootPropsAudio()}
										onClick={() => {
											if (!session) {
												setSignInBoxOpen(true);
												return;
											}
											openDropzoneAudio();
										}}
									>
										{uploadingAudio ? (
											<div className="text-muted-foreground flex h-full flex-col items-center justify-center gap-1 text-sm font-normal">
												<LoaderCircleIcon className="text-muted-foreground animate-spin" />
												<p>Uploading...</p>
											</div>
										) : (
											<div className="text-muted-foreground flex h-full flex-col items-center justify-center gap-1 text-sm font-normal">
												<CloudUpload />
												<p>Click or drag audio here</p>
											</div>
										)}
									</div>
									<input {...getInputPropsAudio()} />
								</div>
							)}
						</div>
					</div>
				</div>

				<div className="flex flex-col items-center justify-center gap-2 md:flex-row">
					{session && !userHasPaid && (
						<div className="flex flex-row items-center gap-2 py-1">
							{/* <div className="flex flex-row items-center gap-1.5 text-sm text-zinc-200">
							<LayersIcon className="size-4" />
							Model
						</div>
						<div className="flex flex-row items-center gap-1 text-sm">
							{["fast", "pro"].map((modelOption) => (
								<Hint key={modelOption} label={modelOption === "fast" ? "Fast model" : "Pro model"} className="text-zinc-200">
									<Button
										key={modelOption}
										size="sm"
										variant="secondary"
										className={cn("font-medium", model === modelOption ? "text-brand-success dark:bg-zinc-700/30" : "text-zinc-300")}
										onClick={() => setModel(modelOption)}
									>
										{capitalize(modelOption)}
										{modelOption === "pro" && !userHasPaid && <CrownIcon className="size-3 text-yellow-500" />}
									</Button>
								</Hint>
							))}
						</div> */}
							<div className="flex flex-row items-center gap-1.5 text-sm text-zinc-300">
								<CrownIcon className="size-4 text-yellow-500" />
								<button
									id={EVENT_OPEN_PLAN_ID}
									onClick={() => setPlanBoxOpen(true)}
									className="text-brand-success underline underline-offset-4"
								>
									Unlock
								</button>{" "}
								high-quality Lip Sync
							</div>
						</div>
					)}
					<SubmitButton
						variant="secondary"
						className="bg-brand-success hover:bg-brand-success/80 h-11 w-full rounded-xl px-8 text-base md:w-auto"
						isSubmitting={submitting}
						onClick={handleGenerate}
						{...{ disabled: submitting || !fileVideo || !fileAudio }}
					>
						<WandSparklesIcon className="mr-2" />
						Generate
						{fileAudio?.fileDuration && (
							<p className="ml-1 flex flex-row items-center gap-1 rounded-lg bg-emerald-600 px-2 py-0.5 text-[11px] font-[350] text-zinc-100">
								<CoinsIcon className="size-3" />
								{Math.floor(fileAudio.fileDuration) * 5}
							</p>
						)}
					</SubmitButton>
				</div>
			</div>

			{(submitting || resultVideo) && (
				<div className="mt-8 flex w-full rounded-xl bg-zinc-800 p-4 md:px-8">
					{submitting && (
						<div className="mx-auto flex flex-col items-center justify-center gap-2 px-4 text-center text-zinc-300">
							<LoaderCircleIcon className="size-4 animate-spin" />
							<span className="font-mono text-xs tabular-nums">{seconds}s</span>
							{/* <div className="flex flex-row items-center gap-1 text-xs">
										<Progress
											value={calculateProgress(Number(seconds), paramsInfo.model.time ?? 60)}
											className="h-1.5 w-[280px] bg-zinc-500"
											indicatorClassName="bg-indigo-600"
										/>
										<span className="font-mono tabular-nums">{calculateProgress(Number(seconds), paramsInfo.model.time ?? 60)}%</span>
									</div> */}
							<div className="text-xs font-[350]">
								Your video is being generated. It might take a few minutes to half an hour. Once finished, the video will be saved in{" "}
								<NoPrefetchLink href="/my-creations" target="_blank" className="text-brand-success underline underline-offset-4">
									My Creations
								</NoPrefetchLink>
								.
							</div>
						</div>
					)}
					{resultVideo && (
						<div className="flex max-h-[480px] w-full justify-center">
							<video src={resultVideo} controls className="h-full object-contain" />
						</div>
					)}
				</div>
			)}
		</>
	);
}
