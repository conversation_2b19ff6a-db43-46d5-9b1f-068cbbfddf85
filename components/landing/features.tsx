import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";

type Feature = {
	title: string;
	description: string;
	image: string;
	imageAlt?: string;
};

export default function FeaturesComponent({ ctaText, ctaUrl, features, badge }: { ctaText: string; ctaUrl: string; features: Feature[]; badge?: string }) {
	return (
		<>
			{features.map((feature, index) => (
				<div key={index} className="container flex flex-col items-center gap-16 px-6 py-24">
					<div className="grid grid-cols-1 items-center gap-8 md:grid-cols-2 lg:gap-16">
						<div
							className={cn(
								"flex h-full flex-col items-center justify-between gap-4 text-center md:items-start md:text-start",
								index % 2 === 0 ? "md:order-last" : "",
							)}
						>
							<div className="flex flex-col gap-2">
								<h2 className="text-[32px] font-semibold text-balance">{feature.title}</h2>
								<p className="text-muted-foreground text-base">{feature.description}</p>
							</div>
							<NoPrefetchLink href={ctaUrl} className={cn(buttonVariants({ size: "lg" }), "h-11 rounded-full")}>
								{ctaText}
							</NoPrefetchLink>
						</div>
						<div className="block w-full rounded-xl border bg-zinc-900 p-1">
							<AspectRatio ratio={3 / 2} className="relative">
								<img
									src={feature.image}
									alt={feature.imageAlt ?? feature.title}
									className="h-full w-full rounded-lg object-cover"
									loading="lazy"
								/>
								{badge && (
									<div
										className="absolute top-2 right-2 rounded-sm bg-zinc-800/80 px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
										style={{ "--content": `'${badge}'` } as React.CSSProperties}
									></div>
								)}
							</AspectRatio>
						</div>
					</div>
				</div>
			))}
		</>
	);
}
