import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { ROUTE_PATH_SIGN_IN } from "@/lib/constants";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";

export default function FinalCTA({
	ctaText = "Start For Free",
	ctaTextDisplay,
	ctaUrl = ROUTE_PATH_SIGN_IN,
	ctaClassName,
	title = "",
	description,
}: {
	ctaText?: string;
	ctaTextDisplay?: boolean;
	ctaUrl?: string;
	ctaClassName?: string;
	title?: string;
	description?: string;
}) {
	return (
		// <div className="flex flex-col items-center gap-12 bg-zinc-900 px-6 py-20">
		<div className="pt-20">
			<div className="gap-12container flex flex-col items-center justify-center gap-10 bg-zinc-900 px-4 py-24 md:px-6">
				<div className="text-center">
					<h2 className="text-3xl font-semibold text-pretty">{title}</h2>
					{description && <p className="text-muted-foreground mx-auto mt-4 max-w-4xl text-pretty">{description}</p>}
				</div>

				<div className="flex flex-col items-center gap-3">
					{ctaTextDisplay ? (
						<NoPrefetchLink
							href={ctaUrl}
							className={cn(buttonVariants({ size: "lg", variant: "secondary" }), "bg-brand-success hover:bg-brand-success/80", ctaClassName)}
						>
							{ctaText}
						</NoPrefetchLink>
					) : (
						<NoPrefetchLink
							href={ctaUrl}
							className={cn(buttonVariants({ size: "lg", variant: "default" }), "h-12 px-8 after:content-(--content)", ctaClassName)}
							style={{ "--content": `'${ctaText}'` } as React.CSSProperties}
						></NoPrefetchLink>
					)}
				</div>
			</div>
		</div>
	);
}
