import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";
import { LucideIcon } from "lucide-react";

export default function BreadcrumbPath({ paths, current }: { paths: { title: string; href: string; icon?: LucideIcon }[]; current: string }) {
	return (
		<Breadcrumb>
			<BreadcrumbList>
				{paths.map((path, index) => (
					<React.Fragment key={index}>
						{index > 0 && <BreadcrumbSeparator />}
						<BreadcrumbItem>
							<NoPrefetchLink href={path.href!} className="flex flex-row items-center gap-1 text-foreground">
								{path.icon && <path.icon className="h-4 w-4" />}
								{path.title}
							</NoPrefetchLink>
						</BreadcrumbItem>
					</React.Fragment>
				))}
				<BreadcrumbSeparator />
				<BreadcrumbItem>
					<span className="text-muted-foreground">{current}</span>
				</BreadcrumbItem>
			</BreadcrumbList>
		</Breadcrumb>
	);
}
