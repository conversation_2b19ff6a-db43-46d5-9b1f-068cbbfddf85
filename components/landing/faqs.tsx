import { ChevronDownIcon } from "lucide-react";

type Faq = {
	question: string;
	answer: string;
};

export default function FAQsComponent({ title, faqs }: { title?: string; faqs: Faq[] }) {
	return (
		<div id="faqs" className="py-20">
			<div className="container flex flex-col items-center justify-center gap-12 px-6">
				<div className="max-w-4xl text-center">
					<h2 className="text-3xl font-semibold text-pretty">{title ?? "Frequently Asked Questions"}</h2>
				</div>
				<div className="w-full max-w-4xl">
					<div className="space-y-2">
						{faqs.map((faq, index) => (
							<details key={index} className="group rounded-lg bg-zinc-900 px-4" open={false}>
								<summary className="flex cursor-pointer list-none items-center justify-between py-4 text-[17px]">
									<h3 className="font-normal">{faq.question}</h3>
									<ChevronDownIcon className="text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200 group-open:rotate-180" />
								</summary>
								<div className="text-muted-foreground grid grid-rows-[0fr] overflow-hidden pb-4 transition-[grid-template-rows] duration-300 ease-in-out group-open:grid-rows-[1fr]">
									<div className="min-h-0">
										<p className="text-sm whitespace-pre-wrap">{faq.answer}</p>
									</div>
								</div>
							</details>
						))}
					</div>
				</div>
			</div>
		</div>
	);
}
