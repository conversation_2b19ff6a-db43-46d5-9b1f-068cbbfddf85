import { sqliteTable, integer, text, index, uniqueIndex } from "drizzle-orm/sqlite-core";

// ==============user====================
export const userSchema = sqliteTable(
	"user",
	{
		pid: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		id: text("uid").unique().notNull(),
		name: text("name").notNull(),
		email: text("email").notNull().unique(),
		emailVerified: integer("email_verified", { mode: "boolean" }).default(false),
		image: text("image"),

		membershipId: integer("membership_id", { mode: "number" }).default(0).notNull(),
		membershipFormatted: text("membership_formatted").default("Free").notNull(),

		creditFree: integer("credit_free").default(0).notNull(),
		creditFreeEndsAt: integer("credit_free_ends_at", { mode: "timestamp_ms" }),
		creditOneTime: integer("credit_onetime").default(0).notNull(),
		creditOneTimeEndsAt: integer("credit_onetime_ends_at", { mode: "timestamp_ms" }),
		creditSubscription: integer("credit_sub").default(0).notNull(),
		creditSubscriptionEndsAt: integer("credit_sub_ends_at", { mode: "timestamp_ms" }),

		subscriptionId: text("sub_id"),
		subscriptionPeriod: text("sub_period").default("none"), // none, month, year
		subscriptionInvoiceEndsAt: integer("sub_invoice_ends_at", { mode: "timestamp_ms" }),
		subscriptionExpireAt: integer("sub_expire_at", { mode: "timestamp_ms" }),

		isDeleted: integer("is_deleted", { mode: "boolean" }).default(false).notNull(),
		ban: integer("ban", { mode: "boolean" }).default(false).notNull(),
		countryCode: text("country_code"),
		ip: text("ip"),
		remark: text("remark"),

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [index("idx_user_ip").on(table.ip)],
);
export type User = typeof userSchema.$inferSelect;
export type NewUser = typeof userSchema.$inferInsert;

// ============== better-auth ====================
export const accountSchema = sqliteTable(
	"better_auth_account",
	{
		pid: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		id: text("uid").unique().notNull(),
		userId: text("user_uid").notNull(),

		providerId: text("provider_id").notNull(),
		accountId: text("account_id").notNull(),

		accessToken: text("access_token"),
		refreshToken: text("refresh_token"),
		accessTokenExpiresAt: integer("access_token_expires_at", { mode: "timestamp_ms" }),
		refreshTokenExpiresAt: integer("refresh_token_expires_at", { mode: "timestamp_ms" }),

		scope: text("scope"),
		idToken: text("id_token"),

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [
		index("idx_better_auth_account_user_uid").on(table.userId),
		uniqueIndex("uidx_better_auth_account_provider").on(table.accountId, table.providerId),
	],
);
export type Account = typeof accountSchema.$inferSelect;
export type NewAccount = typeof accountSchema.$inferInsert;

export const sessionSchema = sqliteTable(
	"better_auth_session",
	{
		pid: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		id: text("uid").unique().notNull(),
		userId: text("user_uid").notNull(),

		token: text("token").notNull(),
		expiresAt: integer("expires_at", { mode: "timestamp_ms" }).notNull(),
		ipAddress: text("ip_address"),
		userAgent: text("user_agent"),

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [index("idx_better_auth_session_token").on(table.token)],
);
export type Session = typeof sessionSchema.$inferSelect;
export type NewSession = typeof sessionSchema.$inferInsert;

export const verificationSchema = sqliteTable(
	"better_auth_verification",
	{
		pid: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		id: text("uid").unique().notNull(),
		identifier: text("identifier").notNull(),
		value: text("value").notNull(),
		expiresAt: integer("expires_at", { mode: "timestamp_ms" }).notNull(),

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [index("idx_better_auth_verification_identifier").on(table.identifier), index("idx_better_auth_verification_expires_at").on(table.expiresAt)],
);
export type Verification = typeof verificationSchema.$inferSelect;
export type NewVerification = typeof verificationSchema.$inferInsert;

// ==============user_credits_history====================
export const userCreditsHistorySchema = sqliteTable(
	"user_credits_history",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		userId: text("user_uid").notNull(),

		type: text("type"),

		creditsFree: integer("credits_free", { mode: "number" }),
		creditsOneTime: integer("credits_one_time", { mode: "number" }),
		creditsSubscription: integer("credits_subscription", { mode: "number" }),

		remark: text("remark"),

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [index("idx_user_credits_history_user_uid").on(table.userId)],
);
export type UserCreditsHistory = typeof userCreditsHistorySchema.$inferSelect;
export type NewUserCreditsHistory = typeof userCreditsHistorySchema.$inferInsert;
