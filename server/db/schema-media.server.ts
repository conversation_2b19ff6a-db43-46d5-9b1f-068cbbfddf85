import { sqliteTable, integer, text, index } from "drizzle-orm/sqlite-core";

// ==============media generate task ===============
export const mediaTaskSchema = sqliteTable(
	"media_task",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		parentTaskId: integer("parent_task_id", { mode: "number" }),

		userId: text("user_uid").notNull(),
		mediaType: text("media_type"),
		tool: text("tool"), // tool name, e.g. lipsync-video-with-audio

		model: text("model"),
		requestBody: text("request_body"),

		thirdRequestId: text("third_request_id"), // API  request id
		status: integer("status", { mode: "number" }).default(0).notNull(),
		visibility: integer("visibility", { mode: "boolean" }).default(false).notNull(),
		mediaHeadUid: text("media_head_uid"),

		creditsSources: text("credits_source"),
		ip: text("ip"),

		remark: text("remark"),
		error: text("error"),
		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.$defaultFn(() => new Date())
			.notNull(),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date())
			.notNull(),
	},
	(table) => [
		index("idx_media_task_user_uid").on(table.userId),
		index("idx_media_task_parent_task_id").on(table.parentTaskId),
		index("idx_media_task_third_request_id").on(table.thirdRequestId),
		index("idx_media_task_status").on(table.status),
	],
);
export type MediaTask = typeof mediaTaskSchema.$inferSelect;
export type NewMediaTask = typeof mediaTaskSchema.$inferInsert;
// ==============media result====================
export const mediaHeadSchema = sqliteTable(
	"media_head",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		uid: text("uid").unique().notNull(),

		userId: text("user_uid").notNull(),
		mediaType: text("media_type"), // image, video
		tool: text("tool"),

		visibility: integer("visibility", { mode: "boolean" }).default(false).notNull(), // false: private, true: public

		mediaPath: text("media_path"),

		status: integer("status", { mode: "number" }).default(0).notNull(), // like: generating, failed, success

		creditsSources: text("credits_source"),

		taskId: integer("task_id"),
		remark: text("remark"),
		errorReason: text("error_reason"),
		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [index("idx_media_head_user_uid").on(table.userId), index("idx_media_head_status").on(table.status)],
);
export type MediaHead = typeof mediaHeadSchema.$inferSelect;
export type NewMediaHead = typeof mediaHeadSchema.$inferInsert;
