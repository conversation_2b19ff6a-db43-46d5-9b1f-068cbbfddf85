import { relations } from "drizzle-orm";
import { sqliteTable, integer, text, index, uniqueIndex } from "drizzle-orm/sqlite-core";

export const changelogSchema = sqliteTable(
	"changelog",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),

		majorVersion: integer("major_version", { mode: "number" }),
		minorVersion: integer("minor_version", { mode: "number" }),
		patchVersion: integer("patch_version", { mode: "number" }),

		title: text("title"),
		image: text("image"),

		publishedAt: integer("publish_date", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [index("idx_changelog_published_at").on(table.publishedAt)],
);
export type Changelog = typeof changelogSchema.$inferSelect;
export type NewChangelog = typeof changelogSchema.$inferInsert;

export const changelogTranslationSchema = sqliteTable(
	"changelog_translation",
	{
		id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
		changelogId: integer("changelog_id", { mode: "number" })
			.notNull()
			.references(() => changelogSchema.id, { onDelete: "cascade" }), // 设置外键和级联删除

		lang: text("lang").default("en").notNull(),
		status: integer("status", { mode: "number" }).default(0).notNull(), // 0: draft, 1: published

		image: text("image"),

		title: text("title").notNull(),
		html: text("html").notNull(),

		publishedAt: integer("publish_date", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),

		createdAt: integer("created_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date()),
		updatedAt: integer("updated_at", { mode: "timestamp_ms" })
			.notNull()
			.$defaultFn(() => new Date())
			.$onUpdateFn(() => new Date()),
	},
	(table) => [
		index("idx_changelog_translation_changelog_id").on(table.changelogId),
		index("idx_changelog_translation_lang").on(table.lang),
		uniqueIndex("uidx_changelog_translation_changelog_id_lang").on(table.changelogId, table.lang),
		index("idx_changelog_translation_status").on(table.status),
		index("idx_changelog_translation_published_at").on(table.publishedAt),
	],
);
export type ChangelogTranslation = typeof changelogTranslationSchema.$inferSelect;
export type NewChangelogTranslation = typeof changelogTranslationSchema.$inferInsert;

export const changelogRelations = relations(changelogSchema, ({ many }) => ({
	translations: many(changelogTranslationSchema),
}));
export const changelogTranslationRelations = relations(changelogTranslationSchema, ({ one }) => ({
	changelog: one(changelogSchema, {
		fields: [changelogTranslationSchema.changelogId],
		references: [changelogSchema.id],
	}),
}));
