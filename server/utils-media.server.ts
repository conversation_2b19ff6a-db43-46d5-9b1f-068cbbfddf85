import { getDB } from "./db/db-client.server";
import { eq } from "drizzle-orm";
import superjson from "superjson";
import { z } from "zod";
import { MediaHead, mediaHeadSchema, MediaTask, mediaTaskSchema } from "./db/schema.server";
import { getKVKeyMediaTask } from "@/lib/utils";
import { getValue, setValue } from "./kv/redis-upstash.server";
import { DURATION_2_HOUR } from "@/lib/constants";

export async function getMediaTask(requestId: string, userId: string | null): Promise<MediaTask | null> {
	let mediaTask: MediaTask | null = null;

	//先从kv中获取
	const cacheKey = getKVKeyMediaTask(requestId);
	const kvDataMediaTask = (await getValue(cacheKey)) as any;
	if (kvDataMediaTask) {
		try {
			mediaTask = superjson.deserialize(kvDataMediaTask) as MediaTask;
		} catch (error) {
			console.error("[getMediaTask] parse media task data from redis error:", error);
		}
	}

	//再从db中获取
	if (!mediaTask) {
		const db = getDB();
		const [newMediaTask]: MediaTask[] = await db.select().from(mediaTaskSchema).where(eq(mediaTaskSchema.thirdRequestId, requestId));
		if (newMediaTask) {
			await setValue(cacheKey, superjson.stringify(newMediaTask), DURATION_2_HOUR);
			mediaTask = newMediaTask;
		}
	}

	if (!mediaTask) {
		return null;
	}

	if (userId && mediaTask.userId !== userId) {
		return null;
	}

	return mediaTask;
}

export async function getMediaHeadRealtime(mediaHeadUid: string, userId: string | null): Promise<MediaHead | null> {
	const db = getDB();
	const [mediaHead]: MediaHead[] = await db.select().from(mediaHeadSchema).where(eq(mediaHeadSchema.uid, mediaHeadUid));
	if (!mediaHead) {
		return null;
	}

	if (userId && mediaHead.userId !== userId) {
		return null;
	}

	return mediaHead;
}

export const ImageResultHeadServerSchema = z.object({
	id: z.number(),
	uid: z.string().nonempty(),
	userId: z.string(),

	prompt: z.string().nullable(),
	filePath: z.string().nullable(),
	fileOriginUrl: z.string().nullable(),

	status: z.number(),
	visibility: z.boolean(),
});
export type ImageResultHeadServer = z.infer<typeof ImageResultHeadServerSchema>;
