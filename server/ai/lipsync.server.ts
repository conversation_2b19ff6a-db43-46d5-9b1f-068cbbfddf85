import { falGenLipSyncWithWebhook } from "./fal-config.server";

export async function genSyncLipsync1_9FromFal(videoUrl: string, audioUrl: string): Promise<string> {
	const falAIEndPoint = "fal-ai/sync-lipsync";

	const payload: any = {
		model: "lipsync-1.9.0-beta",
		video_url: videoUrl,
		audio_url: audioUrl,
		// sync_mode: "bounce",
		sync_mode: "loop",
	};

	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai sync-lipsync 1.9 payload: ", payload);
		console.log("fal.ai sync-lipsync 1.9 falAIEndPoint:", falAIEndPoint);
	}

	const request_id = await falGenLipSyncWithWebhook(falAIEndPoint, payload);
	return request_id;
}

export async function genSyncLipsync2FromFal(videoUrl: string, audioUrl: string): Promise<string> {
	const falAIEndPoint = "fal-ai/sync-lipsync/v2";

	const payload: any = {
		video_url: videoUrl,
		audio_url: audioUrl,
		// sync_mode: "bounce",
		sync_mode: "loop",
	};

	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai sync-lipsync 2 payload: ", payload);
		console.log("fal.ai sync-lipsync 2 falAIEndPoint:", falAIEndPoint);
	}

	const request_id = await falGenLipSyncWithWebhook(falAIEndPoint, payload);
	return request_id;
}
