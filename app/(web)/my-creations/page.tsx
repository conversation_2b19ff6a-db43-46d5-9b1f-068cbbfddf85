import { WEBNAME } from "@/lib/constants";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import SignIn from "@/components/shared/sigin-in";
import type { Metadata } from "next";
import MyCreation from "./my-creation.client";
import { User } from "@/server/db/schema.server";
import { getUser } from "@/server/utils-user.server";
import { userHasPaid } from "@/lib/utils-user";

export const metadata: Metadata = {
	title: `My Creations | ${WEBNAME}`,
	alternates: {
		canonical: "/my-creations",
	},
	robots: {
		index: false,
		follow: false,
	},
};

// async function getHistory(userId: string) {
// 	const db = getDB();
// 	const imageResults = await db
// 		.select({ uid: imageResultSchema.uid, filePath: imageResultSchema.filePath })
// 		.from(imageResultSchema)
// 		.where(and(eq(imageResultSchema.userId, userId), eq(imageResultSchema.status, MediaResultStatus.Completed)))
// 		.orderBy(desc(imageResultSchema.id));
// 	const images = imageResults
// 		.filter((result) => result.filePath)
// 		.map((result) => ({
// 			uid: result.uid,
// 			url: `${OSS_URL_HOST}${result.filePath}`,
// 		}));

// 	return { images };
// }

export default async function Page() {
	// 1. Check user
	const sessionUser = await getCurrentSessionUser();
	let user: User | null = null;
	if (sessionUser) {
		user = await getUser(sessionUser.id);
	}
	if (!user) {
		return (
			<div className="flex h-full min-h-screen items-center justify-center">
				<div className="w-full max-w-md rounded-xl border bg-zinc-900 p-6 shadow-sm">
					<SignIn />
				</div>
			</div>
		);
	}

	// 2. Check user has paid
	if (process.env.NODE_ENV === "development") {
		return <MyCreation isHasPaid={true} />;
	}
	const isHasPaid = userHasPaid(user.membershipId, user.creditOneTimeEndsAt);
	if (isHasPaid) {
		return <MyCreation isHasPaid={true} />;
	}

	return <MyCreation isHasPaid={false} />;
}
