"use client";

import { useEffect, useState } from "react";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ofetch } from "ofetch";
import { AuthError, handleError } from "@/@types/error";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import { MediaHeadType } from "@/@types/media/media-type";
import { BoxIcon, RefreshCcwIcon, RefreshCwIcon } from "lucide-react";
import { PrefetchLink } from "@/components/ui/custom/prefetch-link";
import { Button, buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export default function MyCreation({ isHasPaid }: { isHasPaid: boolean }) {
	const { setSignInBoxOpen } = useSignInBoxOpenStore();

	// ======== Display media ========
	// Pagination
	const [page, setPage] = useState<number>(1);
	const [totalPage, setTotalPage] = useState<number>(1);
	const [totalCount, setTotalCount] = useState<number>(0);
	const pageSize = 20;
	// Fetch media
	const [mediaItems, setMediaItems] = useState<any[]>([]);
	const [taskProgressNumber, setTaskProgressNumber] = useState<number>(0);
	const [isLoading, setIsLoading] = useState(false);
	const fetchMediaItems = async (currentPage: number) => {
		try {
			setIsLoading(true);
			const { status, message, mediaItems, tasks } = await ofetch(
				"/api/v1/user/my-creations",

				{
					method: "POST",
				},
			);
			handleError(status, message);
			setTaskProgressNumber(tasks?.length || 0);
			setMediaItems(mediaItems as { uid: string; url: string; type: MediaHeadType }[]);
			// if (totalPage === 0) {
			// 	setTotalPage(1);
			// } else {
			// 	setTotalPage(totalPage);
			// }
			// setTotalCount(total);
		} catch (error: any) {
			console.error("Failed to fetch:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error("Failed to fetch my creations.");
		} finally {
			setIsLoading(false);
		}
	};
	useEffect(() => {
		if (!isHasPaid) return;
		fetchMediaItems(page);
	}, []);

	return (
		<div className="container flex h-full min-h-screen w-full flex-col px-6 py-4">
			<div className="flex w-full flex-row items-center justify-between gap-2 pt-4 pb-2">
				<div className="mx-auto flex w-full flex-col">
					<h1 className="text-2xl font-medium whitespace-nowrap text-zinc-200">My creations</h1>
					<p className="text-sm text-zinc-400">Showing your creations from the last 30 days</p>
				</div>
				<Button size="lg" variant="secondary" disabled={isLoading} className="text-zinc-300" onClick={() => fetchMediaItems(page)}>
					<RefreshCcwIcon className={`${isLoading ? "animate-spin" : ""}`} /> Refresh
				</Button>
			</div>

			{/* {!isHasPaid && (
				<div className="bg-muted mx-auto mt-20 flex max-w-[360px] flex-col items-center gap-4 rounded-lg border border-zinc-700 p-6 shadow-xs">
					<p className="text-muted-foreground">Upgrade to view your creations</p>
					<Button onClick={() => setPlanBoxOpen(true)} variant="outline" className="">
						✨ Upgrade Now
					</Button>
				</div>
			)} */}

			{!isLoading && !taskProgressNumber && mediaItems.length === 0 && (
				<div className="mt-[120px] flex flex-col items-center gap-4 px-4 py-3">
					<BoxIcon className="size-16 text-zinc-600" />
					<p className="text-xl text-zinc-400">No generation history</p>
					<PrefetchLink href="/" className={cn(buttonVariants({ size: "lg", variant: "secondary" }), "bg-brand-success hover:bg-brand-success/80")}>
						Get Started
					</PrefetchLink>
				</div>
			)}

			{!isLoading && taskProgressNumber > 0 && (
				<div className="mt-2 flex flex-col items-center gap-4 rounded-md bg-zinc-900 px-4 py-3">
					<p className="text-zinc-200">
						You have <span className="text-yellow-500">{taskProgressNumber} task(s)</span> in progress
					</p>
				</div>
			)}

			<div className="mx-auto grid w-full grid-cols-1 gap-2 pt-4 sm:grid-cols-2 md:grid-cols-4">
				{isLoading ? (
					<>
						{Array.from({ length: 12 }).map((_, index) => (
							<div key={index} className="group relative aspect-square w-full rounded border bg-zinc-900">
								<Skeleton className="aspect-square h-full w-full rounded bg-zinc-700/50 object-contain" />
							</div>
						))}
					</>
				) : (
					mediaItems.map((item, index) => (
						<div key={index} className="group relative aspect-square w-full rounded border border-zinc-700 bg-zinc-800">
							<video className="h-full w-full overflow-hidden rounded" controls preload="metadata">
								<source src={item.url} />
								Your browser does not support the video tag.
							</video>
							{/* <div
								className={cn(
									"absolute right-2 bottom-2 items-center gap-1",
									downloadingImageUid === item.uid ? "opacity-100" : "group-hover:opacity-100 sm:opacity-0",
								)}
							>
								<SubmitButton
									isSubmitting={downloading && downloadingImageUid === item.uid}
									size="icon"
									variant="secondary"
									className="shrink-0 cursor-pointer"
									onClick={async () => {
										try {
											setDownloadingImageUid(item.uid);
											setDownloading(true);
											if (item.type === MediaHeadType.Image) {
												const base64 = await imageUrlToBase64(item.url, {
													noCache: true,
													hasWatermark: userHasPaid ? false : true,
												});
												await downloadImageFromBase64(base64);
											} else {
												await downloadImageFromUrl(item.url);
											}
										} catch (error) {
											console.error("Failed to download image:", error);
										} finally {
											setDownloadingImageUid(undefined);
											setDownloading(false);
										}
									}}
								>
									<Download />
								</SubmitButton>
							</div> */}
						</div>
					))
				)}
			</div>
		</div>
	);
}
