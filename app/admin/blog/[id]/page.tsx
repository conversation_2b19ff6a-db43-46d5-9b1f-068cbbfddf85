import { notFound } from "next/navigation";
import { BlogPostTranslation, blogPostTranslationSchema } from "@/server/db/schema.server";
import { getDB } from "@/server/db/db-client.server";
import { desc, eq } from "drizzle-orm";
import EditBlogComponent from "../edit-blog-component";

async function getBlogPostTranslation(id: number) {
	const db = getDB();
	const [blogPost]: BlogPostTranslation[] = (await db
		.select()
		.from(blogPostTranslationSchema)
		.where(eq(blogPostTranslationSchema.id, id))
		.orderBy(desc(blogPostTranslationSchema.id))) as BlogPostTranslation[];
	return blogPost;
}

type Params = Promise<{ id: number }>;
export default async function Page({ params }: { params: Params }) {
	const { id } = await params;
	const blogPost = await getBlogPostTranslation(id);
	if (!blogPost) {
		return notFound();
	}
	return <EditBlogComponent blogId={id} blog={blogPost} />;
}
