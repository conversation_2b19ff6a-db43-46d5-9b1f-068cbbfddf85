import { notFound } from "next/navigation";
import { blogPostSchema } from "@/server/db/schema.server";
import { getDB } from "@/server/db/db-client.server";
import { desc } from "drizzle-orm";
import BlogHeadsComponent from "./blog-heads.client";
import { checkAuthAdmin } from "@/server/auth/check-auth-admin";
import { BlogPostWithPartialTranslations } from "@/@types/admin/blog/blog";

async function getBlogPosts() {
	if (!checkAuthAdmin()) {
		notFound();
	}

	const db = getDB();
	const posts: BlogPostWithPartialTranslations[] = await db.query.blogPostSchema.findMany({
		with: {
			translations: {
				columns: {
					id: true,
					postId: true,
					lang: true,
					status: true,
					title: true,
					publishedAt: true,
					createdAt: true,
					updatedAt: true,
				},
			},
		},
		orderBy: [desc(blogPostSchema.createdAt)],
		// limit: pageSize,
		// offset: offset,
	});
	return posts;
}

export default async function Page() {
	const blogPosts = await getBlogPosts();

	return <BlogHeadsComponent blogPosts={blogPosts} />;
}
