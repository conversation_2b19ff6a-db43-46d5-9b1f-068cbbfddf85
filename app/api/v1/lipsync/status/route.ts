import { NextResponse } from "next/server";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { getMediaHeadRealtime, getMediaTask } from "@/server/utils-media.server";
import { MediaResultStatus } from "@/@types/media/media-type";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { MediaHead, MediaTask } from "@/server/db/schema.server";
import { handleApiError } from "@/@types/error-api";

interface Params {
	id: string;
}

/**
 * get lipsync generation status
 */
export async function POST(req: Request) {
	const params: Params = await req.json();
	if (!params.id) {
		return NextResponse.json({ status: 400, message: "The params is invalid." });
	}

	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	const userId = sessionUser.id;
	if (process.env.NODE_ENV === "development") {
		console.log("params: ", params);
		console.log("userId: ", userId);
	}

	try {
		const mediaTask: MediaTask | null = await getMediaTask(params.id, userId);
		if (!mediaTask) {
			return NextResponse.json({ status: 404, message: "Generate task not found." });
		}
		if (mediaTask.status === MediaResultStatus.Completed) {
			const mediaHead: MediaHead | null = await getMediaHeadRealtime(mediaTask.mediaHeadUid!, userId);
			if (!mediaHead) {
				return NextResponse.json({ status: 404, message: "Task reuslt not found." });
			}
			return NextResponse.json({
				status: 200,
				taskStatus: mediaTask.status,
				resultUrl: `${OSS_URL_HOST}${mediaHead.mediaPath}`,
			});
		}
		return NextResponse.json({ status: 200, taskStatus: mediaTask.status, taskError: mediaTask.error });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/lipsync/status`);
	}
}
