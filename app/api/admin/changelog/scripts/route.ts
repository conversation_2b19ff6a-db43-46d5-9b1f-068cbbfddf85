import { getDB } from "@/server/db/db-client.server";
import { changelogSchema, changelogTranslationSchema } from "@/server/db/schema.server";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
	return NextResponse.json({ message: "Not found." }, { status: 404 });
	const db = getDB();

	try {
		console.log("开始插入测试数据...");

		// 插入第一个changelog
		const [changelog1] = await db
			.insert(changelogSchema)
			.values({
				majorVersion: 1,
				minorVersion: 2,
				patchVersion: 0,
				title: "Major Feature Update",
				publishedAt: new Date("2024-01-15"),
			})
			.returning({ id: changelogSchema.id });

		// 为第一个changelog插入多语言版本
		await db.insert(changelogTranslationSchema).values([
			{
				changelogId: changelog1.id,
				lang: "en",
				status: 1, // Published
				title: "Major Feature Update - New AI Lip Sync Technology",
				html: "<h2>What's New</h2><p>We've introduced revolutionary AI-powered lip sync technology that provides more accurate and natural results.</p><ul><li>Improved accuracy by 40%</li><li>Faster processing times</li><li>Better support for multiple languages</li></ul>",
				publishedAt: new Date("2024-01-15"),
			},
			{
				changelogId: changelog1.id,
				lang: "zh",
				status: 1, // Published
				title: "重大功能更新 - 全新AI唇形同步技术",
				html: "<h2>新功能介绍</h2><p>我们推出了革命性的AI驱动唇形同步技术，提供更准确、更自然的效果。</p><ul><li>准确度提升40%</li><li>处理速度更快</li><li>更好地支持多种语言</li></ul>",
				publishedAt: new Date("2024-01-15"),
			},
			{
				changelogId: changelog1.id,
				lang: "es",
				status: 0, // Draft
				title: "Actualización de Funciones Principales - Nueva Tecnología de Sincronización Labial AI",
				html: "<h2>Novedades</h2><p>Hemos introducido una tecnología revolucionaria de sincronización labial impulsada por IA que proporciona resultados más precisos y naturales.</p>",
				publishedAt: new Date("2024-01-15"),
			},
		]);

		// 插入第二个changelog
		const [changelog2] = await db
			.insert(changelogSchema)
			.values({
				majorVersion: 1,
				minorVersion: 1,
				patchVersion: 5,
				title: "Bug Fixes and Performance Improvements",
				publishedAt: new Date("2024-01-10"),
			})
			.returning({ id: changelogSchema.id });

		// 为第二个changelog插入多语言版本
		await db.insert(changelogTranslationSchema).values([
			{
				changelogId: changelog2.id,
				lang: "en",
				status: 1, // Published
				title: "Bug Fixes and Performance Improvements",
				html: "<h2>Improvements</h2><p>This release focuses on stability and performance enhancements.</p><ul><li>Fixed memory leak issues</li><li>Improved video processing speed</li><li>Enhanced error handling</li></ul>",
				publishedAt: new Date("2024-01-10"),
			},
			{
				changelogId: changelog2.id,
				lang: "zh",
				status: 1, // Published
				title: "错误修复和性能改进",
				html: "<h2>改进内容</h2><p>此版本专注于稳定性和性能增强。</p><ul><li>修复内存泄漏问题</li><li>提升视频处理速度</li><li>增强错误处理</li></ul>",
				publishedAt: new Date("2024-01-10"),
			},
		]);

		// 插入第三个changelog
		const [changelog3] = await db
			.insert(changelogSchema)
			.values({
				majorVersion: 1,
				minorVersion: 1,
				patchVersion: 0,
				title: "UI/UX Improvements",
				publishedAt: new Date("2024-01-05"),
			})
			.returning({ id: changelogSchema.id });

		// 为第三个changelog插入单语言版本
		await db.insert(changelogTranslationSchema).values([
			{
				changelogId: changelog3.id,
				lang: "en",
				status: 1, // Published
				title: "UI/UX Improvements and New Dashboard",
				html: "<h2>User Interface Updates</h2><p>We've redesigned the user interface for better usability and modern look.</p><ul><li>New dashboard design</li><li>Improved navigation</li><li>Dark mode support</li><li>Mobile responsive improvements</li></ul>",
				publishedAt: new Date("2024-01-05"),
			},
		]);

		// 插入第四个changelog（只有基础信息，没有翻译）
		await db.insert(changelogSchema).values({
			majorVersion: 1,
			minorVersion: 0,
			patchVersion: 8,
			title: "Initial Beta Release",
		});

		console.log("测试数据插入成功！");
		console.log("已创建:");
		console.log("- 4个changelog条目");
		console.log("- 多个语言版本（英文、中文、西班牙文）");
		console.log("- 不同的发布状态（已发布、草稿）");
	} catch (error) {
		console.error("插入测试数据时出错:", error);
	}
	return NextResponse.json({ status: 200 });
}
