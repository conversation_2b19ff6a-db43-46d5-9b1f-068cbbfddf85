import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { checkAuthAdmin } from "@/server/auth/check-auth-admin";
import { ChangelogWithPartialTranslations } from "@/@types/admin/changelog/changelog";
import { changelogSchema } from "@/server/db/schema.server";
import { desc } from "drizzle-orm";

export async function GET(req: Request) {
	if (!checkAuthAdmin()) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	// 从URL查询参数中获取分页信息
	const { searchParams } = new URL(req.url);
	const page = parseInt(searchParams.get("page") || "1");
	const pageSize = parseInt(searchParams.get("pageSize") || "10");

	// 验证分页参数
	if (page < 1 || pageSize < 1 || pageSize > 100) {
		return NextResponse.json({ status: 400, message: "Invalid pagination parameters." });
	}

	const offset = (page - 1) * pageSize;

	if (process.env.NODE_ENV === "development") {
		console.log("pagination params:", { page, pageSize, offset });
	}

	const db = getDB();

	// 获取总数
	const totalCount = await db.$count(changelogSchema);

	// 获取分页数据
	const changelogs: ChangelogWithPartialTranslations[] = await db.query.changelogSchema.findMany({
		with: {
			translations: {
				columns: {
					id: true,
					changelogId: true,
					lang: true,
					status: true,
					title: true,
					publishedAt: true,
					createdAt: true,
					updatedAt: true,
				},
			},
		},
		orderBy: [desc(changelogSchema.updatedAt)],
		limit: pageSize,
		offset: offset,
	});

	return NextResponse.json({
		status: 200,
		changelogs,
		pagination: {
			page,
			pageSize,
			totalCount,
			totalPages: Math.ceil(totalCount / pageSize),
		},
	});
}
