import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { deleteValue } from "@/server/kv/redis-upstash.server";
import { checkAuthAdmin } from "@/server/auth/check-auth-admin";
import { ChangelogSchemaReqType, changelogTypeReqSchema } from "@/@types/admin/changelog/changelog";
import { getKVKeyChangelogHeads } from "@/lib/utils";

export async function POST(req: Request) {
	if (!checkAuthAdmin()) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	const params: ChangelogSchemaReqType = await req.json();
	// console.log("params:", params);
	try {
		changelogTypeReqSchema.parse(params);
	} catch (error) {
		return NextResponse.json({ status: 400, message: "The params is invalid." });
	}

	// return NextResponse.json({ status: 500, message: "ok" });

	let updateData: any = {
		title: params.title,
		lang: params.lang,
		majorVersion: params.majorVersion,
		minorVersion: params.minorVersion,
		patchVersion: params.patchVersion,
		html: params.html,
		status: params.status,
		publishedAt: new Date(params.publishedAt),
	};
	// if (params.image) {
	// 	updateData.image = params.image;
	// }
	// if (params.html) {
	// 	updateData.html = params.html;
	// }
	// if (params.publishedAt) {
	// 	updateData.publishedAt = params.publishedAt;
	// }
	if (params.status) {
		updateData.status = params.status;
	}

	let changelogs;

	const db = getDB();
	// if (params.id) {
	// 	// update
	// 	changelogs = await db
	// 		.update(changelogItemSchema)
	// 		.set({
	// 			...updateData,
	// 			updatedAt: new Date(),
	// 		})
	// 		.where(eq(changelogItemSchema.id, params.id))
	// 		.returning({
	// 			id: changelogItemSchema.id,
	// 		});
	// } else {
	// 	// insert
	// 	changelogs = await db
	// 		.insert(changelogItemSchema)
	// 		.values({
	// 			...updateData,
	// 		})
	// 		.onConflictDoNothing({
	// 			target: changelogItemSchema.id,
	// 		})
	// 		.returning({
	// 			id: changelogItemSchema.id,
	// 		});
	// }

	// delete changelog heads kv cache
	// await deleteValue(getKVKeyChangelogHeads(1));

	// if (changelogs.length > 0) {
	// 	return NextResponse.json({ status: 200, message: "ok", newChangelogId: changelogs[0].id });
	// }
	return NextResponse.json({ status: 200, message: "ok" });
}
