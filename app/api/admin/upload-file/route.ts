import { NextResponse } from "next/server";
import { OSS_URL_HOST } from "@/lib/constants";
import { getCurrentMonthAndDayString } from "@/lib/utils-date";
import { createR2Url } from "@/server/r2.server";
import { checkAuthAdmin } from "@/server/auth/check-auth-admin";

interface Params {
	fileExtension: string | undefined | null;
	contentType: string | undefined | null;
	prefixType: string;
}
export async function POST(req: Request) {
	if (!checkAuthAdmin()) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	const params: Params = await req.json();
	if (!params.fileExtension || !params.contentType || !params.prefixType) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	const fileId = crypto.randomUUID();
	const filename = fileId + params.fileExtension;
	const { yearMonth, yearMonthDay } = getCurrentMonthAndDayString();
	let url_suffix = "";
	if (process.env.NODE_ENV === "development") {
		url_suffix = `dev/${params.prefixType}/${yearMonth}/${yearMonthDay}${filename}`;
	} else {
		url_suffix = `${params.prefixType}/${yearMonth}/${yearMonthDay}${filename}`;
	}

	try {
		const signedUrl = await createR2Url(url_suffix, params.contentType as string);
		return NextResponse.json({ status: 200, url: signedUrl, method: "PUT", file_url: `${OSS_URL_HOST}${url_suffix}` });
	} catch (error: any) {
		return NextResponse.json({ status: 500, message: error.message || "Failed to upload file." });
	}
}
