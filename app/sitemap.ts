import { MetadataRoute } from "next";
// import { i18nConfig } from "@/i18n-config";
import { WEBHOST } from "@/lib/constants";

export const dynamic = "force-static";

interface SitemapEntry {
	url: string;
	lastModified?: Date;
	changeFrequency?: "monthly" | "daily" | "always" | "hourly" | "weekly" | "yearly" | "never";
	priority?: number;
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
	const baseUrl = WEBHOST;

	const nowDate = new Date();

	const normal = ["/pricing", "/changelog", "/terms", "/privacy"].map((url) => ({
		url: baseUrl + url,
		lastModified: nowDate,
	}));

	return [{ url: baseUrl, lastModified: nowDate }, ...normal] as SitemapEntry[];

	// //legal terms
	// const terms: SitemapEntry[] = [{ url: baseUrl + "/terms-of-use" }, { url: baseUrl + "/privacy-policy" }];

	// //by locale
	// const localePages: () => SitemapEntry[] = () => {
	// 	let site: SitemapEntry[] = [];
	// 	i18nConfig.locales.map((locale) => {
	// 		let baseLocaleUrl = baseUrl;
	// 		if (locale !== i18nConfig.defaultLocale) {
	// 			baseLocaleUrl += "/" + locale;
	// 		}
	// 		site.push(
	// 			...[
	// 				{ url: baseLocaleUrl },
	// 				// { url: baseLocaleUrl + "/changelog" },
	// 				// { url: baseLocaleUrl + "/pricing" }
	// 			],
	// 		);
	// 	});
	// 	return site;
	// };

	// return [...localePages(), ...terms];
}
