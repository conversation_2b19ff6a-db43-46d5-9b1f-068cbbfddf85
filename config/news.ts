export type newsType = {
	title: string;
	url: string;
	publishedAt: Date;
};

export const newsAll: newsType[] = [
	// {
	// 	title: "FastLipsync now featured on Scamadviser",
	// 	url: "https://www.scamadviser.com/check-website/FastLipsync.art",
	// 	publishedAt: new Date("2025-08-02"),
	// },
	// {
	// 	title: "FastLipsync now featured on Huzzler",
	// 	url: "https://huzzler.so/products/ROGfEHJIjV/FastLipsync",
	// 	publishedAt: new Date("2025-08-01"),
	// },
	// {
	// 	title: "FastLipsync now shared on velog",
	// 	url: "https://velog.io/@ielliot/FastLipsync-all-in-one-ai-image-and-video-creation-platform",
	// 	publishedAt: new Date("2025-07-08"),
	// },
	// {
	// 	title: "FastLipsync now shared on Zenn",
	// 	url: "https://zenn.dev/elliot/articles/629a6b4d330c8f",
	// 	publishedAt: new Date("2025-07-08"),
	// },
	// {
	// 	title: "FastLipsync now featured on Lovable Launched",
	// 	url: "https://launched.lovable.dev/FastLipsync",
	// 	publishedAt: new Date("2025-07-02"),
	// },
	// {
	// 	title: "FastLipsync now listed on Crunchbase",
	// 	url: "https://www.crunchbase.com/organization/FastLipsync",
	// 	publishedAt: new Date("2025-07-02"),
	// },
	// {
	// 	title: "FastLipsync now can be found on Homepage.Ninja",
	// 	url: "https://homepage.ninja/ielliot",
	// 	publishedAt: new Date("2025-06-29"),
	// },
	// {
	// 	title: "FastLipsync now can be found on Website Informer",
	// 	url: "https://website.informer.com/FastLipsync.art",
	// 	publishedAt: new Date("2025-06-28"),
	// },
	// {
	// 	title: "FastLipsync now can be found on Webwiki",
	// 	url: "https://www.webwiki.com/FastLipsync.art",
	// 	publishedAt: new Date("2025-06-25"),
	// },
	// {
	// 	title: "FastLipsync now can be found on SiteLike",
	// 	url: "https://www.sitelike.org/similar/FastLipsync.art/",
	// 	publishedAt: new Date("2025-06-15"),
	// },
	// {
	// 	title: "FastLipsync now can be found on Website Carbon Calculator",
	// 	url: "https://www.websitecarbon.com/website/FastLipsync-art",
	// 	publishedAt: new Date("2025-06-14"),
	// },
	// {
	// 	title: "FastLipsync Launches: Your AI Image and Video Creation Platform",
	// 	url: "https://pastelink.net/b0zpatj5",
	// 	publishedAt: new Date("2025-06-13"),
	// },
	// {
	// 	title: "FastLipsync now shared on Tumblr",
	// 	url: "https://www.tumblr.com/ielliot/786111427753721856/FastLipsync-free-all-in-one-ai-image-generator?source=share",
	// 	publishedAt: new Date("2025-06-12"),
	// },
];

export type mentionType = {
	name: string;
	url: string;
};
export const mentionsAll: mentionType[] = [
	{
		name: "Github",
		url: "https://github.com/elliotodyl",
	},
	{
		name: "Pinterest",
		// url: "https://www.pinterest.com/elliotroams/FastLipsync/",
		url: "https://www.pinterest.com/pin/629941066668261621/",
	},
	{
		name: "Behance",
		url: "https://www.behance.net/ielliot",
	},
	{
		name: "Linktree",
		url: "https://linktr.ee/ielliot",
	},
	{
		name: "Bento",
		url: "https://bento.me/ielliot",
	},
	{
		name: "Biolinky",
		url: "https://biolinky.co/elliot",
	},
	{
		name: "Vocal",
		url: "https://vocal.media/authors/elliot-g18fs0jwk",
	},
	{
		name: "about.me",
		url: "https://about.me/theelliot",
	},
	{
		name: "AllMyLinks",
		url: "https://allmylinks.com/ielliot",
	},
	{
		name: "solo.to",
		url: "https://solo.to/ielliot",
	},
	// {
	// 	name: "GetAllMyLinks",
	// 	url: "https://getallmylinks.com/elliot",
	// },
	{
		name: "ShippingExplorer",
		url: "https://www.shippingexplorer.net/en/user/ielliot/166851",
	},
	{
		name: "Onvasortir",
		url: "https://dunkerque.onvasortir.com/profil_read.php?Theelliot",
	},
	{
		name: "MyAnimeList",
		url: "https://myanimelist.net/profile/theelliot",
	},
	{
		name: "wallhaven",
		url: "https://wallhaven.cc/user/ielliot",
	},
	{
		name: "ModWorkshop",
		url: "https://modworkshop.net/user/elliot1",
	},
	{
		name: "SoundCloud",
		url: "https://soundcloud.com/theelliot",
	},
	{
		name: "lit.link",
		url: "https://lit.link/en/ielliot",
	},
	{
		name: "Gravatar",
		url: "https://gravatar.com/supernaturally8ccc69295c",
	},
	{
		name: "GrepMed",
		url: "https://www.grepmed.com/elliot",
	},
	{
		name: "M5Stack Community",
		url: "https://community.m5stack.com/user/ielliot",
	},
];
